<div class="shop-area bg py-90">
    <div class="container">
        <div class="row">
            <div class="col-lg-3">
                <div class="shop-sidebar">
                    <div class="shop-widget">
                        <h4 class="shop-widget-title">{{ "web.global.category"|translate}}</h4>
                        <ul class="shop-category-list">
                            {% if Model.Categories != null and Model.Categories.size > 0 %}
                                {% for category in Model.Categories %}
                                    <li>
                                        <a href="/collections/{{ category.PageUrl }}">
                                            {{ category.Category_en }}
                                            {% if category.SubCateCount > 0 %}
                                                <span>({{ category.SubCateCount }})</span>
                                            {% endif %}
                                        </a>
                                    </li>
                                {% endfor %}
                            {% else %}
                                <li>
                                    <a href="#">
                                        No Categories Found
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </div>
                    <div class="shop-widget">
                        <h4 class="shop-widget-title">Brands</h4>
                        <ul class="shop-checkbox-list">
                            <li>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="brand1"/>
                                    <label class="form-check-label" for="brand1">
                                        Tovol<span>(12)</span>
                                    </label>
                                </div>
                            </li>
                            <li>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="brand2"/>
                                    <label class="form-check-label" for="brand2">
                                        Sundoy<span>(15)</span>
                                    </label>
                                </div>
                            </li>
                            <li>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="brand3"/>
                                    <label class="form-check-label" for="brand3">
                                        Sahoo Medoc<span>(20)</span>
                                    </label>
                                </div>
                            </li>
                            <li>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="brand4"/>
                                    <label class="form-check-label" for="brand4">
                                        Casterly<span>(05)</span>
                                    </label>
                                </div>
                            </li>
                            <li>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="brand5"/>
                                    <label class="form-check-label" for="brand5">
                                        Maindeno<span>(09)</span>
                                    </label>
                                </div>
                            </li>
                            <li>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="brand6"/>
                                    <label class="form-check-label" for="brand6">
                                        Knroll Seproll<span>(25)</span>
                                    </label>
                                </div>
                            </li>
                            <li>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="brand7"/>
                                    <label class="form-check-label" for="brand7">
                                        Neo Enternity<span>(19)</span>
                                    </label>
                                </div>
                            </li>
                            <li>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="brand8"/>
                                    <label class="form-check-label" for="brand8">
                                        Charisha<span>(23)</span>
                                    </label>
                                </div>
                            </li>
                            <li>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="brand9"/>
                                    <label class="form-check-label" for="brand9">
                                        Audou<span>(13)</span>
                                    </label>
                                </div>
                            </li>
                            <li>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="brand10"/>
                                    <label class="form-check-label" for="brand10">
                                        Desioreck<span>(14)</span>
                                    </label>
                                </div>
                            </li>
                            <li>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="brand11"/>
                                    <label class="form-check-label" for="brand11">
                                        Rochel Brek<span>(16)</span>
                                    </label>
                                </div>
                            </li>
                            <li>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="brand12"/>
                                    <label class="form-check-label" for="brand12">
                                        Mordani<span>(17)</span>
                                    </label>
                                </div>
                            </li>
                            <li>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="brand13"/>
                                    <label class="form-check-label" for="brand13">
                                        Others<span>(18)</span>
                                    </label>
                                </div>
                            </li>
                        </ul>
                    </div>
                    <div class="shop-widget-banner mt-30 mb-50">
                        <div class="banner-img" style="background-image:url(/assets/img/banner/shop-banner.jpg)"></div>
                        <div class="banner-content">
                            <h6>
                                Get <span>35% Off</span>
                            </h6>
                            <h4>New Collection of Medicine</h4>
                            <a href="#" class="theme-btn">{{ "products.goods.shopNow"|translate}}</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-9">
                <div class="col-md-12">
                    <div class="shop-sort">
                        <div class="shop-sort-box">
                            <div class="shop-sorty-label">{{ "products.lists.sort_by"|translate}}:</div>
                            <select class="select" onchange="changeSorting()">
                                <option value="Default" {% if Model.SortBy=="Default" %}selected{% endif %}>
                                    {{ "products.lists.sort_by_ary_0"|translate}}
                                </option>
                                <option value="latest" {% if Model.SortBy=="latest" %}selected{% endif %}>
                                    {{ "products.lists.sort_by_ary_1"|translate}}
                                </option>
                                <option value="bestseller" {% if Model.SortBy=="bestseller" %}selected{% endif %}>
                                    {{ "products.lists.sort_by_ary_5"|translate}}
                                </option>
                                <option value="priceasc" {% if Model.SortBy=="priceasc" %}selected{% endif %}>
                                    {{ "products.lists.sort_by_ary_3"|translate}}
                                </option>
                                <option value="pricedesc" {% if Model.SortBy=="pricedesc" %}selected{% endif %}>
                                    {{ "products.lists.sort_by_ary_4"|translate}}
                                </option>
                            </select>
                            <div class="shop-sort-show">
                                {% if Model.TotalItems > 0 %}
                                    Showing {{ Model.ShowingStart }}-{{ Model.ShowingEnd }} of {{ Model.TotalItems }} Results
                                {% else %}
                                    No products found
                                {% endif %}
                            </div>
                        </div>
                        <div class="shop-sort-gl">
                            <a href="/collections"
                               class="shop-sort-grid active">
                                <i class="far fa-grid-round-2"></i>
                            </a>
                            <a href="/collections/ShopList" class="shop-sort-list">
                                <i class="far fa-list-ul"></i>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="shop-item-wrap item-4">
                    <div class="row g-4">
                        {% if Model.Products != null and Model.Products.size > 0 %}
                            {% for product in Model.Products %}
                                <div class="col-md-6 col-lg-4">
                                    <div class="product-item">
                                        <div class="product-img">
                                            {% if product.TypeLabel != null and product.TypeLabel != "" %}
                                                <span class="type {% if product.TypeLabel == "Hot" %}hot{% elsif product.TypeLabel == "Out Of Stock" %}oos{% endif %}">{{ product.TypeLabel }}</span>
                                            {% endif %}
                                            <a href="/products/{{ product.PageUrl }}">
                                                <img src="{% if product.PicPath != null and product.PicPath != '' %}{{ product.PicPath }}{% else %}{{ static_path }}/assets/img/product/01.png{% endif %}"
                                                     alt="{{ product.ProductName }}"/>
                                            </a>
                                            <div class="product-action-wrap">
                                                <div class="product-action">
                                                    <!-- 隐藏的快速预览按钮 -->
                                                    <a href="#" data-bs-toggle="modal" data-bs-target="#quickview"
                                                       data-tooltip="tooltip" title="{{ "web.global.quickView"|translate}}"
                                                       data-product-id="{{ product.ProductId }}"
                                                       class="quick-view-btn" style="display: none;">
                                                        <i class="far fa-eye"></i>
                                                    </a>
                                                    <!-- 收藏按钮 -->
                                                    <a href="#" data-tooltip="tooltip" title="{{ "products.goods.addToFavorites"|translate}}"
                                                       class="add-to-wishlist"
                                                       data-product-id="{{ product.ProductId }}"
                                                       data-is-favorited="{% if product.IsFavorited %}true{% else %}false{% endif %}">
                                                        {% if product.IsFavorited %}
                                                            <i class="fas fa-heart"></i>
                                                        {% else %}
                                                            <i class="far fa-heart"></i>
                                                        {% endif %}
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="product-content">
                                            <h3 class="product-title">
                                                <a href="/products/{{ product.PageUrl }}">
                                                   {{ product.ProductName }}</a>
                                            </h3>
                                            <div class="product-rate">
                                                {% assign fullStars = product.Rating | floor %}
                                                {% assign halfStar = product.Rating | minus: fullStars %}
                                                {% assign nextStar = fullStars | plus: 1 %}
                                                {% for i in (1..5) %}
                                                    {% if i <= fullStars %}
                                                        <i class="fas fa-star"></i>
                                                    {% elsif halfStar >= 0.5 and i == nextStar %}
                                                        <i class="fas fa-star-half-alt"></i>
                                                    {% else %}
                                                        <i class="far fa-star"></i>
                                                    {% endif %}
                                                {% endfor %}
                                            </div>
                                            <div class="product-bottom">
                                                <div class="product-price" data-price-container
                                                     data-original-price="{{ product.OriginalPriceFormat }}"
                                                     data-current-price="{{ product.PriceFormat }}"
                                                     data-promotion-price="{{ product.PromotionPriceFormat }}">
                                                    <!-- 价格内容将通过JavaScript动态生成 -->
                                                    {% if product.PromotionPriceFormat and product.PromotionPriceFormat != "" %}
                                                        <!-- 有促销价：显示促销价作为当前价，原价作为划线价 -->
                                                        {% if product.OriginalPriceFormat and product.OriginalPriceFormat != "" %}
                                                            <del>{{ product.OriginalPriceFormat }}</del>
                                                        {% endif %}
                                                        <span>{{ product.PromotionPriceFormat }}</span>
                                                    {% elsif product.OriginalPriceFormat and product.OriginalPriceFormat != "" %}
                                                        <!-- 没有促销价但有原价：显示当前价和原价 -->
                                                        <del>{{ product.OriginalPriceFormat }}</del>
                                                        <span>{{ product.PriceFormat }}</span>
                                                    {% else %}
                                                        <!-- 只显示当前价格 -->
                                                        <span>{{ product.PriceFormat }}</span>
                                                    {% endif %}
                                                </div>
                                                {% comment %}<button type="button" class="product-cart-btn btn-addto-cart" data-bs-placement="left"{% endcomment %}
                                                        {% comment %}data-tooltip="tooltip" title="{{ "products.goods.addToCart"|translate}}"{% endcomment %}
                                                        {% comment %}{% if product.IsInStock == false %}disabled{% endif %}{% endcomment %}
                                                        {% comment %}data-product-id="{{ product.ProductId }}">{% endcomment %}
                                                    {% comment %}<i class="far fa-shopping-bag"></i>{% endcomment %}
                                                {% comment %}</button>{% endcomment %}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        {% else %}
                            <div class="col-12 text-center">
                                <p>{{ "products.goods.no_products"|translate}}.</p>
                            </div>
                        {% endif %}
                    </div>
                </div>

                <div class="pagination-area mt-50">
                    <div aria-label="Page navigation example">
                        <ul class="pagination">
                            {% assign prevPage = Model.CurrentPage | minus: 1 %}
                            <li class="page-item {% if Model.CurrentPage == 1 %}disabled{% endif %}">
                                <a class="page-link" href="javascript:void(0)" onclick="changePage({{ prevPage }})"
                                   aria-label="Previous">
                                    <span aria-hidden="true"><i class="far fa-arrow-left"></i></span>
                                </a>
                            </li>

                            {% if Model.TotalPages <= 5 %}
                                {% for i in (1..Model.TotalPages) %}
                                    <li class="page-item {% if i == Model.CurrentPage %}active{% endif %}">
                                        <a class="page-link" href="javascript:void(0)"
                                           onclick="changePage({{ i }})">{{ i }}</a>
                                    </li>
                                {% endfor %}
                            {% else %}
                                <li class="page-item {% if Model.CurrentPage == 1 %}active{% endif %}">
                                    <a class="page-link" href="javascript:void(0)" onclick="changePage(1)">1</a>
                                </li>

                                {% if Model.CurrentPage > 3 %}
                                    <li class="page-item"><span class="page-link">...</span></li>
                                {% endif %}

                                {% if Model.CurrentPage > 2 %}
                                    <li class="page-item">
                                        <a class="page-link" href="javascript:void(0)"
                                           onclick="changePage({{ prevPage }})">{{ prevPage }}</a>
                                    </li>
                                {% endif %}

                                {% if Model.CurrentPage != 1 and Model.CurrentPage != Model.TotalPages %}
                                    <li class="page-item active">
                                        <a class="page-link" href="javascript:void(0)">{{ Model.CurrentPage }}</a>
                                    </li>
                                {% endif %}

                                {% assign nextPage = Model.CurrentPage | plus: 1 %}
                                {% if nextPage < Model.TotalPages %}
                                    <li class="page-item">
                                        <a class="page-link" href="javascript:void(0)"
                                           onclick="changePage({{ nextPage }})">{{ nextPage }}</a>
                                    </li>
                                {% endif %}

                                {% assign twoLess = Model.TotalPages | minus: 2 %}
                                {% if Model.CurrentPage < twoLess %}
                                    <li class="page-item"><span class="page-link">...</span></li>
                                {% endif %}

                                <li class="page-item {% if Model.CurrentPage == Model.TotalPages %}active{% endif %}">
                                    <a class="page-link" href="javascript:void(0)"
                                       onclick="changePage({{ Model.TotalPages }})">{{ Model.TotalPages }}</a>
                                </li>
                            {% endif %}

                            {% assign nextPage = Model.CurrentPage | plus: 1 %}
                            <li class="page-item {% if Model.CurrentPage == Model.TotalPages or Model.TotalPages == 0 %}disabled{% endif %}">
                                <a class="page-link" href="javascript:void(0)" onclick="changePage({{ nextPage }})"
                                   aria-label="Next">
                                    <span aria-hidden="true"><i class="far fa-arrow-right"></i></span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>

            </div>
        </div>
    </div>
    <div class="modal quickview fade" id="quickview" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
         aria-labelledby="quickview" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
            <div class="modal-content">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
                            class="far fa-xmark"></i></button>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-lg-6 col-md-12 col-sm-12 col-xs-12">
                            <div class="quickview-img">
                                <img src="{{ static_path }}/assets/img/product/04.png" alt="#">
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-12 col-sm-12 col-xs-12">
                            <div class="quickview-content">
                                <h4 class="quickview-title">Surgical Face Mask</h4>
                                <div class="quickview-rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star-half-alt"></i>
                                    <i class="far fa-star"></i>
                                    <span class="rating-count"> (4 Customer Reviews)</span>
                                </div>
                                <div class="quickview-price">
                                    <h5>
                                        <del>$860</del>
                                        <span>$740</span></h5>
                                </div>
                                <ul class="quickview-list">
                                    <li>Brand:<span>Medica</span></li>
                                    <li>Category:<span>Healthcare</span></li>
                                    <li>Stock:<span class="stock">Available</span></li>
                                    <li>Code:<span>789FGDF</span></li>
                                </ul>
                                <div class="quickview-cart">
                                    <a href="#" class="theme-btn">{{ "products.goods.addToCart"|translate}}</a>
                                </div>
                                <div class="quickview-social">
                                    <span>Share:</span>
                                    <a href="#"><i class="fab fa-facebook-f"></i></a>
                                    <a href="#"><i class="fab fa-x-twitter"></i></a>
                                    <a href="#"><i class="fab fa-pinterest-p"></i></a>
                                    <a href="#"><i class="fab fa-instagram"></i></a>
                                    <a href="#"><i class="fab fa-linkedin-in"></i></a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // 页面切换函数
    function changePage(pageNumber) {
        if (pageNumber < 1 || pageNumber > {{ Model.TotalPages }} || pageNumber == {{ Model.CurrentPage }}) {
            return;
        }

        // 构建带有页码的URL
        let url = '/collections';
        const categoryUrl = '{{ Model.CategoryUrl }}';
        if (categoryUrl) {
            url += '/' + categoryUrl;
        }

        let params = [];

        if (pageNumber > 1) {
            params.push(`page=${pageNumber}`);
        }

        const keyword = '{{ Model.Keyword }}';
        if (keyword) {
            params.push(`keyword=${encodeURIComponent(keyword)}`);
        }

        const sortBy = '{{ Model.SortBy }}';
        if (sortBy && sortBy !== 'Default') {
            params.push(`sortBy=${encodeURIComponent(sortBy)}`);
        }

        const includeSoldOut = {{ Model.IncludeSoldOut | downcase }};
        if (includeSoldOut) {
            params.push(`includeSoldOut=${includeSoldOut}`);
        }

        if (params.length > 0) {
            url += '?' + params.join('&');
        }

        // 使用HTMX加载下一页
        htmx.ajax('GET', url, '#main');
        // 更新浏览器历史记录
        history.pushState(null, '', url);
    }

    // 排序切换函数
    function changeSorting() {
        const sortBy = document.querySelector('.shop-sort-box .select').value;

        // 构建URL
        let url = '/collections';
        let params = [];

        const page = {{ Model.CurrentPage }};
        if (page > 1) {
            params.push(`page=${page}`);
        }

        const keyword = '{{ Model.Keyword }}';
        if (keyword) {
            params.push(`keyword=${encodeURIComponent(keyword)}`);
        }

        const categoryUrl = '{{ Model.CategoryUrl }}';
        if (categoryUrl) {
            params.push(`categoryUrl=${encodeURIComponent(categoryUrl)}`);
        }

        if (sortBy && sortBy !== 'Default') {
            params.push(`sortBy=${encodeURIComponent(sortBy)}`);
        }

        const includeSoldOut = {{ Model.IncludeSoldOut | downcase }};
        if (includeSoldOut) {
            params.push(`includeSoldOut=${includeSoldOut}`);
        }

        if (params.length > 0) {
            url += '?' + params.join('&');
        }
        // 使用HTMX加载排序后的页面
        // htmx.ajax('GET', url, '#main');
        // history.pushState(null, '', url);
        // 直接跳转到新的URL，不使用htmx
        window.location.href = url;
    }

</script>

<script>
    // 页面加载完成后处理所有图片URL
    document.addEventListener('DOMContentLoaded', function() {
        // 处理商品网格视图中的所有产品图片
        const productImages = document.querySelectorAll('.product-item img');
        productImages.forEach(function(img) {
            if (img.src && !ImageUrlHelper.hasOssParams(img.src)) {
                img.src = ImageUrlHelper.getMediumUrl(img.src);
            }
            if (img.dataset.src && !ImageUrlHelper.hasOssParams(img.dataset.src)) {
                img.dataset.src = ImageUrlHelper.getMediumUrl(img.dataset.src);
            }
        });

        // 处理快速预览模态框中的图片
        const quickViewImages = document.querySelectorAll('#quickview img');
        quickViewImages.forEach(function(img) {
            if (img.src && !ImageUrlHelper.hasOssParams(img.src)) {
                img.src = ImageUrlHelper.getMediumUrl(img.src);
            }
            if (img.dataset.src && !ImageUrlHelper.hasOssParams(img.dataset.src)) {
                img.dataset.src = ImageUrlHelper.getMediumUrl(img.dataset.src);
            }
        });

        // 处理购物车按钮点击事件，阻止默认弹窗，改为打开快速预览
        document.addEventListener('click', function (e) {
            const addToCartBtn = e.target.closest('.btn-addto-cart');
            if (addToCartBtn) {
                e.preventDefault();
                e.stopPropagation();

                // 获取产品ID
                const productId = addToCartBtn.getAttribute('data-product-id');
                if (!productId) {
                    console.error('Product ID not found');
                    return;
                }

                // 查找对应的隐藏的快速查看按钮并触发点击
                const quickViewBtn = addToCartBtn.closest('.product-item').querySelector('.quick-view-btn');
                if (quickViewBtn) {
                    quickViewBtn.click();
                } else {
                    console.error('Quick view button not found');
                }
            }

            // 处理收藏按钮点击事件
            const wishlistBtn = e.target.closest('.add-to-wishlist');
            if (wishlistBtn) {
                e.preventDefault();
                e.stopPropagation();

                // 直接从wishlist按钮获取产品ID和收藏状态
                const productId = wishlistBtn.getAttribute('data-product-id');
                const isFavorited = wishlistBtn.getAttribute('data-is-favorited') === 'true';

                if (!productId) {
                    if (typeof customize_pop !== 'undefined' && customize_pop.warning) {
                        customize_pop.warning('Unable to get product ID', null, null, { showIcon: false });
                    } else {
                        alert('Unable to get product ID');
                    }
                    return;
                }

                // 根据当前收藏状态决定操作
                const url = isFavorited ? '/Account/RemoveFromWishlistByProductId' : '/Account/AddToWishlist';
                const requestBody = { productId: parseInt(productId) };

                // 发送收藏请求
                fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestBody)
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // 根据操作类型显示不同的成功消息
                            const message = isFavorited ? 'Removed from wishlist successfully' : 'Added to wishlist successfully';
                            if (typeof customize_pop !== 'undefined' && customize_pop.success) {
                                customize_pop.success(message, null, null, { showIcon: false });
                            } else {
                                alert(message);
                            }

                            // 更新按钮状态
                            const heartIcon = wishlistBtn.querySelector('i');
                            if (heartIcon) {
                                if (isFavorited) {
                                    heartIcon.className = 'far fa-heart'; // 改为空心心形
                                    wishlistBtn.setAttribute('data-is-favorited', 'false');
                                } else {
                                    heartIcon.className = 'fas fa-heart'; // 改为实心心形
                                    wishlistBtn.setAttribute('data-is-favorited', 'true');
                                }
                            }
                        } else {
                            if (typeof customize_pop !== 'undefined' && customize_pop.warning) {
                                customize_pop.warning(data.message || 'Operation failed', null, null, { showIcon: false });
                            } else {
                                alert(data.message || 'Operation failed');
                            }
                        }
                    })
                    .catch(error => {
                        console.error('收藏操作时出错:', error);
                        if (typeof customize_pop !== 'undefined' && customize_pop.error) {
                            customize_pop.error('Network error occurred', null, null, { showIcon: false });
                        } else {
                            alert('Network error occurred');
                        }
                    });
            }
        });
    });
</script>

