
<script src="{{static_path}}/assets/js/modernizr.min.js"></script>
<script src="{{static_path}}/assets/js/bootstrap.bundle.min.js"></script>
<script src="{{static_path}}/assets/js/imagesloaded.pkgd.min.js"></script>
<script src="{{static_path}}/assets/js/jquery.magnific-popup.min.js"></script>
<script src="{{static_path}}/assets/js/isotope.pkgd.min.js"></script>
<script src="{{static_path}}/assets/js/jquery.appear.min.js"></script>
<script src="{{static_path}}/assets/js/jquery.easing.min.js"></script>
<script src="{{static_path}}/assets/js/owl.carousel.min.js"></script>
<script src="{{static_path}}/assets/js/counter-up.js"></script>
<script src="{{static_path}}/assets/js/jquery-ui.min.js"></script>
<script src="{{static_path}}/assets/js/jquery.nice-select.min.js"></script>
<script src="{{static_path}}/assets/js/countdown.min.js"></script>
<script src="{{static_path}}/assets/js/wow.min.js"></script>
<script src="{{static_path}}/assets/js/flex-slider.js"></script>
<script src="{{static_path}}/assets/js/main.js"></script>

<!-- 全局业务脚本 - 只加载一次 -->
<script src="/businessJs/imageUrlHelper.js"></script>
<script src="/js/Pop-ups/frame-message.js"></script>
<script src="/businessJs/Common/priceUtils.js"></script>
<script src="/businessJs/Product/Index/quickView_t600.js"></script>
<script src="/businessJs/Product/Index/priceDisplay_Index_t600.js"></script>

<script>
    // 设置静态资源路径全局变量
    window.staticPath = '{{static_path}}';

    // 添加HTMX事件监听器来管理加载状态
    document.addEventListener('htmx:beforeRequest', function(evt) {
        // 请求开始前显示加载状态
        if (window.customize_pop && customize_pop.loading) {
            customize_pop.loading('正在加载...');
        }
    });

    document.addEventListener('htmx:afterRequest', function(evt) {
        // 请求完成后关闭加载状态
        if (window.customize_pop && customize_pop.loadingClose) {
            customize_pop.loadingClose();
        }

        // 如果请求失败，显示错误信息
        if (!evt.detail.successful) {
            if (window.customize_pop && customize_pop.error) {
                customize_pop.error('页面加载失败，请重试', null, null, { showIcon: false });
            } else {
                alert('页面加载失败，请重试');
            }
        }
    });

    document.addEventListener('htmx:responseError', function(evt) {
        // 响应错误时的处理
        if (window.customize_pop) {
            if (customize_pop.loadingClose) {
                customize_pop.loadingClose();
            }
            if (customize_pop.error) {
                customize_pop.error('网络错误，请检查网络连接', null, null, { showIcon: false });
            }
        } else {
            alert('网络错误，请检查网络连接');
        }
    });
</script>